import {inject} from '@loopback/core';
import {repository, Where} from '@loopback/repository';
import {
  post,
  requestBody,
  RestBindings,
  Request,
  Response,
  api
} from '@loopback/rest';
import {OrganizationPlanRepository, OrganizationRepository} from '../repositories';
import {FeatureService} from '../services/feature.service';
import {StripeBillingService} from '../services/stripe/stripe-billing.service';
import Stripe from 'stripe';
import {GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class StripeWebhookController {
  private stripe: Stripe;

  constructor(
    @repository(OrganizationPlanRepository) protected orgPlanRepository: OrganizationPlanRepository,
    @repository(OrganizationRepository) protected orgRepository: OrganizationRepository,
    @inject(RestBindings.Http.REQUEST) private request: Request,
    @inject(RestBindings.Http.RESPONSE) private response: Response,
    @inject('services.FeatureService') private featureService: FeatureService,
    @inject('services.StripeBillingService') private stripeBillingService: StripeBillingService
  ) {
    // Use the same API key logic as the StripeBillingService
    const apiKey = process.env.NODE_ENV === 'production'
      ? (process.env.STRIPE_SECRET_KEY || '***********************************************************************************************************')
      : (process.env.STRIPE_TEST_KEY || 'rk_test_51MTp7XIox80A0GuIecPlRIVnj7jUSoymHUt046pfnYjo9g3czqihRVmaimalEcKrzAZY2VquLHkRCLkDDNlHvpc400hqc0KAXz');

    this.stripe = new Stripe(apiKey, {
      apiVersion: '2025-04-30.basil',
    });
  }

  @post('/stripe/webhooks', {
    responses: {
      '200': {
        description: 'Handle Stripe webhook events',
      },
    },
  })
  @skipGuardCheck()
  async handleWebhook(
    @requestBody({
			content: {
			  'application/json': {
				schema: {},
			  },
			},
		  }) body: any
  ): Promise<{received: boolean}> {
    const sig = this.request.headers['stripe-signature'] as string;

    // Dynamically select webhook secret based on environment
    // Always prioritize production key, only use dev key in non-production environments
    let webhookSecret: string;

    if (process.env.STRIPE_WEBHOOK_SECRET) {
      // If production webhook secret is set, always use it (most fail-safe)
      webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    } else if (process.env.NODE_ENV === 'production') {
      // Production environment but no env var set - use hardcoded production fallback
      webhookSecret = 'whsec_cDqra14eQAuUOnT9DG4OQhlIvopA5ovM';
    } else {
      // Non-production environment - use dev webhook secret
      webhookSecret = process.env.STRIPE_WEBHOOK_SECRET_DEV || 'whsec_sT8WQqjGxfpQKwkXkwDTTIyEKqJw9VTg';
    }


	// set payload to raw text body
	const payload = (this.request as any).rawBody;

    if (!webhookSecret) {
      console.warn('No Stripe webhook secret found. Skipping signature verification.');
      return this.processWebhookEvent(payload as unknown as Stripe.Event);
    }

    let event: Stripe.Event;

    try {
      event = this.stripe.webhooks.constructEvent(
        payload,
        sig,
        webhookSecret
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      this.response.status(400).send(`Webhook Error: ${err.message}`);
      return {received: false};
    }

    return this.processWebhookEvent(event);
  }

  private async processWebhookEvent(event: Stripe.Event): Promise<{received: boolean}> {
    console.log(`Processing Stripe webhook event: ${event.type}`);


    try {
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
          break;

        // case 'customer.subscription.created':
        //   await this.handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        //   break;

        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
          break;

        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
          break;

        case 'invoice.paid':
          // Handle invoice paid events
          await this.handleSubscriptionInvoiceEvent(event.data.object as Stripe.Invoice, true);
          break;

        case 'invoice.payment_failed':
          // Handle invoice payment failed events
          await this.handleSubscriptionInvoiceEvent(event.data.object as Stripe.Invoice, false);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return {received: true};
    } catch (error) {
      console.error(`Error processing webhook event ${event.type}:`, error);
      throw error;
    }
  }

  private async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<void> {
    console.log('Checkout session completed:', session.id);

    if (!session.metadata?.orgId) {
      console.warn('Checkout session has no orgId in metadata, skipping');
      return;
    }

    const orgId = parseInt(session.metadata.orgId);
    const subscriptionId = session.subscription as string;
	const checkoutId = session.id;

    if (!subscriptionId) {
      console.warn('Checkout session has no subscription ID, skipping');
      return;
    }

    // Find the pending org plan
    const orgPlan = await this.orgPlanRepository.findOne({
      where: {
        orgId: orgId,
        status: 'PENDING',
        subscriptionId: checkoutId // Use checkout ID to find the pending plan
      }
    });

    if (!orgPlan) {
      console.warn(`No pending plan found for org ${orgId}, checking for ones with session ID`);

      // Check if we already have an entry with this subscription ID (duplicate webhook)
      const existingPlan = await this.orgPlanRepository.findOne({
        where: {
          orgId: orgId,
          subscriptionId: subscriptionId
        }
      });

      if (existingPlan) {
        console.log(`Found existing plan with subscription ID ${subscriptionId}, skipping`);
        return;
      }

      // Otherwise, this is an error condition - we should have a pending plan
      console.error(`No pending plan found for org ${orgId}`);
      return;
    }

    // Update the org plan with the subscription ID and set status to ACTIVE
    await this.orgPlanRepository.updateById(orgPlan.id, {
      subscriptionId: subscriptionId,
      status: 'PENDING',
      lastPaymentDate: new Date().toISOString(),
    });

    // Update features for the organization
    await this.featureService.updateLoyaltyAndGwpEnabled(orgId);

    console.log(`Org ${orgId} subscription ${subscriptionId} activated`);
  }

//   private async handleSubscriptionCreated(subscription: Stripe.Subscription): Promise<void> {
//     console.log('Subscription created:', subscription.id);

//     if (!subscription.metadata?.orgId) {
//       console.warn('Subscription has no orgId in metadata, skipping');
//       return;
//     }

//     const orgId = parseInt(subscription.metadata.orgId);

//     // Check if we already have an entry for this subscription
//     const existingPlan = await this.orgPlanRepository.findOne({
//       where: {
//         orgId: orgId,
//         subscriptionId: subscription.id
//       }
//     });

//     if (existingPlan) {
//       console.log(`Found existing plan with subscription ID ${subscription.id}, skipping`);

//       // Update status if needed based on subscription status
//       if (subscription.status === 'active' && existingPlan.status !== 'ACTIVE') {
//         await this.orgPlanRepository.updateById(existingPlan.id, {
//           status: 'ACTIVE',
//           lastPaymentDate: new Date().toISOString(),
//         });

//         // Update features
//         await this.featureService.updateLoyaltyAndGwpEnabled(orgId);

//         console.log(`Updated existing plan status to ACTIVE for subscription ${subscription.id}`);
//       }

//       return;
//     }

//     // Find a pending org plan for this organization
//     const pendingOrgPlan = await this.orgPlanRepository.findOne({
//       where: {
//         orgId: orgId,
//         status: 'PENDING',
//         subscriptionId: {eq: undefined}
//       }
//     });

//     if (pendingOrgPlan) {
//       // Update the pending plan with this subscription
//       await this.orgPlanRepository.updateById(pendingOrgPlan.id, {
//         subscriptionId: subscription.id,
//         status: subscription.status === 'active' ? 'ACTIVE' : 'PENDING',
//         lastPaymentDate: subscription.status === 'active' ? new Date().toISOString() : undefined,
//       });

//       console.log(`Updated pending org plan ${pendingOrgPlan.id} with subscription ${subscription.id}`);

//       // If subscription is active, update features
//       if (subscription.status === 'active') {
//         await this.featureService.updateLoyaltyAndGwpEnabled(orgId);
//       }

// 	  this.stripeBillingService.cancelLegacyShopifySubscriptions(orgId).catch((err) => {
// 		console.error(`Error cancelling legacy Shopify subscriptions for org ${orgId}:`, err);
// 	  });

//       return;
//     }

//     // If we get here, we have a subscription without a matching plan record
//     // This could be due to a race condition or out-of-sync state
//     console.warn(`No matching plan found for subscription ${subscription.id}, org ${orgId}`);
//     console.log(`Creating new org plan record for subscription ${subscription.id}`);

//     try {
//       // Create a new org plan entry with the subscription
//       // We'll try to determine the plan ID based on the product
//       const subscriptionItems = await this.stripe.subscriptionItems.list({
//         subscription: subscription.id,
//       });

//       if (!subscriptionItems.data.length) {
//         console.warn(`No items found for subscription ${subscription.id}`);
//         return;
//       }

//       const item = subscriptionItems.data[0];
//       const productId = item.price.product as string;

//       // Get the product to extract the plan name
//       const product = await this.stripe.products.retrieve(productId);
//       const planName = product.name;

//       // Try to find a matching plan by name
//       const plans = await this.findPlansByName(planName);

//       if (!plans.length) {
//         console.warn(`No matching plan found for product name: ${planName}`);
//         return;
//       }

//       // Use the first matching plan
//       const planId = plans[0].id;
//       console.log(`Found matching plan ${planId} for product ${planName}`);

//       // Check if there's an existing plan for this org+planId combo
//       const existingOrgPlan = await this.orgPlanRepository.findOne({
//         where: {
//           orgId: orgId,
//           planId: planId
//         }
//       });

//       if (existingOrgPlan) {
//         // Update the existing plan
//         await this.orgPlanRepository.updateById(existingOrgPlan.id, {
//           subscriptionId: subscription.id,
//           status: subscription.status === 'active' ? 'ACTIVE' : 'PENDING',
//           lastPaymentDate: subscription.status === 'active' ? new Date().toISOString() : undefined,
//         });

//         console.log(`Updated existing org plan ${existingOrgPlan.id} with subscription ${subscription.id}`);
//       } else {
//         // Create a new org plan entry
//         const newOrgPlan = await this.orgPlanRepository.create({
//           orgId: orgId,
//           planId: planId,
//           subscriptionId: subscription.id,
//           status: subscription.status === 'active' ? 'ACTIVE' : 'PENDING',
//           lastPaymentDate: subscription.status === 'active' ? new Date().toISOString() : undefined,
//         });

//         console.log(`Created new org plan ${newOrgPlan.id} for subscription ${subscription.id}`);
//       }

//       // If subscription is active, update features
//       if (subscription.status === 'active') {
//         await this.featureService.updateLoyaltyAndGwpEnabled(orgId);
//       }
//     } catch (error) {
//       console.error(`Error creating org plan for subscription ${subscription.id}:`, error);
//     }
//   }

  private async handleSubscriptionUpdated(subscription: Stripe.Subscription): Promise<void> {
    console.log('Subscription updated:', subscription.id);

    if (!subscription.metadata?.orgId) {
      console.warn('Subscription has no orgId in metadata, skipping');
      return;
    }

    const orgId = parseInt(subscription.metadata.orgId);

    // Find any plans using this subscription ID
    const orgPlans = await this.orgPlanRepository.find({
      where: {
        orgId: orgId,
        subscriptionId: subscription.id
      }
    });

    if (!orgPlans.length) {
      console.warn(`No org plans found for subscription ${subscription.id}`);
      return;
    }

    // Handle cancel_at_period_end initiated from Stripe side
    if (subscription.cancel_at_period_end) {
      console.log(`Subscription ${subscription.id} has cancel_at_period_end=true, scheduling cancellation`);

      // Get the period end date from the subscription
      let periodEndDate = null;

      // Try to get current_period_end from subscription items first (same logic as billing service)
      if ((subscription as any).items?.data?.length > 0) {
        const firstItem = (subscription as any).items.data[0];
        if (firstItem.current_period_end) {
          periodEndDate = new Date(firstItem.current_period_end * 1000);
          console.log(`Found period end in subscription item: ${periodEndDate.toISOString()}`);
        }
      }

      // Fallback to subscription level current_period_end
      if (!periodEndDate && (subscription as any).current_period_end) {
        periodEndDate = new Date((subscription as any).current_period_end * 1000);
        console.log(`Found period end in subscription: ${periodEndDate.toISOString()}`);
      }

      // Last resort fallback
      if (!periodEndDate) {
        console.warn(`Could not determine period end date for subscription ${subscription.id}. Using 30 days from now.`);
        periodEndDate = new Date();
        periodEndDate.setDate(periodEndDate.getDate() + 30);
        console.log(`Using fallback period end date: ${periodEndDate.toISOString()}`);
      }

      // Update all plans for this subscription to have a scheduled end date
      for (const plan of orgPlans) {
        if (plan.status === 'ACTIVE' && !plan.scheduledEnd) {
          await this.orgPlanRepository.updateById(plan.id, {
            scheduledEnd: periodEndDate.toISOString()
          });
          console.log(`Set scheduledEnd for plan ${plan.id} to ${periodEndDate.toISOString()} due to Stripe cancel_at_period_end`);
        }
      }

      return;
    }

	if (subscription.status === 'active') {
		// Check if this is a scheduled plan that should become active
		const pendingPlan = orgPlans.find(plan => plan.status === 'PENDING_NEXT_RENEWAL');

		if (pendingPlan) {
			console.log(`Not updating status for pending plan ${pendingPlan.id} - it will become active on next renewal`);
			return;
		} else {
			// Regular subscription activation
			await this.orgPlanRepository.updateById(orgPlans[0].id, {
				status: 'ACTIVE',
				lastPaymentDate: new Date().toISOString(),
			});
			console.log(`Updated org plan ${orgPlans[0].id} status to ACTIVE`);
		}

		// Update features for this organization
		await this.featureService.updateLoyaltyAndGwpEnabled(orgId);
		return;
	}

	return this.handleSubscriptionDeleted(subscription);
  }

  private async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    console.log('Subscription deleted:', subscription.id);

    if (!subscription.metadata?.orgId) {
      console.warn('Subscription has no orgId in metadata, skipping');
      return;
    }

    const orgId = parseInt(subscription.metadata.orgId);

    // Find all plans using this subscription ID
    const orgPlans = await this.orgPlanRepository.find({
      where: {
        orgId: orgId,
        subscriptionId: subscription.id
      }
    });

    if (!orgPlans.length) {
      console.warn(`No org plans found for subscription ${subscription.id}`);
      return;
    }

    console.log(`Processing ${orgPlans.length} plans for subscription ${subscription.id} deletion`);

    // Check if any of the plans have a scheduledEnd date (delayed cancellation)
    const plansWithScheduledEnd = orgPlans.filter(plan => plan.scheduledEnd);

    if (plansWithScheduledEnd.length > 0) {
      console.log(`Found ${plansWithScheduledEnd.length} plans with scheduled end dates. Ignoring immediate Stripe cancellation webhook.`);

      // Just clear the subscription ID but keep the plan active until scheduledEnd
      for (const plan of plansWithScheduledEnd) {
        await this.orgPlanRepository.updateById(plan.id, {
          subscriptionId: undefined,  // Remove the subscription ID reference since it's cancelled in Stripe
        });
        console.log(`Cleared subscription ID for plan ${plan.id} but keeping it active until scheduled end: ${plan.scheduledEnd}`);
      }

      // Handle any plans without scheduled end normally
      const plansWithoutScheduledEnd = orgPlans.filter(plan => !plan.scheduledEnd);
      for (const plan of plansWithoutScheduledEnd) {
        await this.orgPlanRepository.updateById(plan.id, {
          status: 'CANCELLED',
          subscriptionId: undefined,
          scheduledEnd: undefined,
          scheduledStart: undefined
        });
        console.log(`Updated org plan ${plan.id} status to CANCELLED due to subscription deletion (no scheduled end)`);
      }
    } else {
      // No scheduled end dates, handle normally
      console.log(`No plans with scheduled end dates found. Marking all plans as CANCELLED.`);
      for (const plan of orgPlans) {
        await this.orgPlanRepository.updateById(plan.id, {
          status: 'CANCELLED',
          subscriptionId: undefined,  // Remove the subscription ID reference
          scheduledEnd: undefined,
          scheduledStart: undefined
        });

        console.log(`Updated org plan ${plan.id} status to CANCELLED due to subscription deletion`);
      }
    }

    // No longer auto-switch to free plan - leave all plans as CANCELLED
    console.log(`Subscription ${subscription.id} deleted. Plans marked as CANCELLED. No auto-switch to free plan.`);

    // Update features to reflect the plan change
    await this.featureService.updateLoyaltyAndGwpEnabled(orgId);
  }

  /**
   * Handle invoice events (paid or payment failed)
   */
  private async handleSubscriptionInvoiceEvent(invoice: any, isPaid: boolean): Promise<void> {
    console.log(`Invoice ${invoice.id} ${isPaid ? 'paid' : 'payment failed'}`);

    // Get the subscription ID safely
    if (!invoice.subscription) {
      console.log(`Invoice ${invoice.id} is not associated with a subscription`);
      return;
    }

    // Convert to string regardless of the type
    const subscriptionId = typeof invoice.subscription === 'string'
      ? invoice.subscription
      : (invoice.subscription as any).id || String(invoice.subscription);

    // Get the subscription details
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

      if (!subscription.metadata?.orgId) {
        console.warn(`Subscription ${subscriptionId} has no orgId in metadata, skipping`);
        return;
      }

      const orgId = parseInt(subscription.metadata.orgId);

      // Find org plans for this subscription
      const orgPlans = await this.orgPlanRepository.find({
        where: {
          orgId: orgId,
          subscriptionId: subscriptionId
        }
      });

      if (!orgPlans.length) {
        console.warn(`No org plans found for subscription ${subscriptionId}`);
        return;
      }

      // Handle invoice status updates
      for (const plan of orgPlans) {
        if (isPaid) {
          // Invoice was paid - update the payment date but check status first
          if (plan.status === 'PENDING_NEXT_RENEWAL') {
            // Don't change status for pending plans - they will become active on next renewal
            await this.orgPlanRepository.updateById(plan.id, {
              lastPaymentDate: new Date().toISOString()
            });
            console.log(`Updated payment date for pending plan ${plan.id} - keeping PENDING_NEXT_RENEWAL status`);
          } else {
            // Regular plan - update payment date and ensure status is ACTIVE
            await this.orgPlanRepository.updateById(plan.id, {
              lastPaymentDate: new Date().toISOString(),
              status: 'ACTIVE'
            });
            console.log(`Updated payment date and status to ACTIVE for org plan ${plan.id}`);
          }

          // Cancel all other active subscriptions for this organization
          await this.stripeBillingService.cleanupSubscriptions(orgId, subscriptionId);
        } else {
          // Payment failed - update status to reflect past due
          if (plan.status === 'ACTIVE') {
            await this.orgPlanRepository.updateById(plan.id, {
              status: 'PAST_DUE'
            });

            console.log(`Updated org plan ${plan.id} status to PAST_DUE due to payment failure`);
          }
        }
      }

      // Update features if the invoice was paid
      if (isPaid) {
        await this.featureService.updateLoyaltyAndGwpEnabled(orgId);

        // Cancel any free trial plans (ACTIVE plans without subscription ID) for this organization
        const freeTrialPlans = await this.orgPlanRepository.find({
          where: {
            orgId: orgId,
            status: 'ACTIVE',
            subscriptionId: null as any
          }
        });

        if (freeTrialPlans.length > 0) {
          console.log(`Found ${freeTrialPlans.length} free trial plans to cancel for org ${orgId} after invoice payment`);

          for (const freeTrialPlan of freeTrialPlans) {
            await this.orgPlanRepository.updateById(freeTrialPlan.id, {
              status: 'CANCELLED'
            });
            console.log(`Cancelled free trial plan ${freeTrialPlan.id} (planId: ${freeTrialPlan.planId}) for org ${orgId}`);
          }
        }
      }
    } catch (error) {
      console.error(`Error processing invoice ${invoice.id}:`, error);
    }
  }

  /**
   * Find plans by name
   *
   * Helper method to find plans matching a given name
   */
  private async findPlansByName(planName: string): Promise<any[]> {
    // Since this is a helper method, we'll use a simple approach
    try {
      // Inject the PlanRepository dependency at the controller level would be better,
      // but for now we'll just access it through the constructor injection
      const plans = await this.orgRepository.execute(
        'SELECT * FROM Plan WHERE LOWER(name) = LOWER(?)',
        [planName]
      );

      // Ensure we return an array
      if (!plans) return [];
      return Array.isArray(plans) ? plans : [plans];
    } catch (error) {
      console.error('Error finding plans by name:', error);
      return [];
    }
  }
}
